# Railway-specific Dockerfile for Frontend deployment
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./
COPY apps/www/package.json ./apps/www/
COPY packages/eslint-config/package.json ./packages/eslint-config/
COPY packages/typescript-config/package.json ./packages/typescript-config/

# Install dependencies
RUN npm ci

# Build the application
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build only the frontend
RUN cd apps/www && npm run build

# Production image with serve
FROM base AS runner
WORKDIR /app

# Install serve globally
RUN npm install -g serve

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 reactrouter

# Copy built application
COPY --from=builder /app/apps/www/build ./build

USER reactrouter

EXPOSE 3000

ENV PORT=3000
ENV NODE_ENV=production

CMD ["serve", "build", "-s", "-l", "3000"]
