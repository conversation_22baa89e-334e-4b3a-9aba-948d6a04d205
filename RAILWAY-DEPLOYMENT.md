# 🚂 Railway.app Deployment Guide

## 🎯 Quick Fix for Current Error

The error "No start command could be found" happens because Railway is trying to deploy the entire monorepo. Here's how to fix it:

### Option 1: Deploy Server Only (Recommended for Testing)

1. **Use the root `railway.json`** (already configured)
2. **Set environment variables** in Railway dashboard:
   ```
   NODE_ENV=production
   PORT=3000
   ```
3. **Redeploy** - Railway will now find the start command

### Option 2: Deploy Both Apps Separately (Recommended for Production)

#### Step 1: Deploy Server
1. **Create new Railway service** for "Server"
2. **Connect to your GitHub repo**
3. **Set root directory** to `apps/server` in Railway settings
4. **Set environment variables**:
   ```
   NODE_ENV=production
   PORT=3000
   ```

#### Step 2: Deploy Frontend
1. **Create another Railway service** for "Frontend"
2. **Connect to same GitHub repo**
3. **Set root directory** to `apps/www` in Railway settings
4. **Set environment variables**:
   ```
   NODE_ENV=production
   VITE_API_URL=https://your-server-service.railway.app
   ```

## 🔧 Alternative: Use Dockerfile Deployment

If Nixpacks continues to have issues, switch to Docker:

### In Railway Dashboard:
1. **Go to Settings** → **Build**
2. **Change Builder** from "Nixpacks" to "Dockerfile"
3. **Set Dockerfile Path**:
   - Server: `apps/server/Dockerfile`
   - Frontend: `apps/www/Dockerfile`

## 📋 Complete Railway Setup

### 1. Server Service Configuration
```
Service Name: revisa-server
Builder: Nixpacks (or Dockerfile)
Root Directory: apps/server (if using separate services)
Start Command: node dist/main.js
Environment Variables:
  NODE_ENV=production
  PORT=3000
  DATABASE_URL=postgresql://... (if using Railway PostgreSQL)
```

### 2. Frontend Service Configuration
```
Service Name: revisa-frontend
Builder: Nixpacks (or Dockerfile)
Root Directory: apps/www (if using separate services)
Start Command: npx serve build -s -l 3000
Environment Variables:
  NODE_ENV=production
  VITE_API_URL=https://revisa-server.railway.app
```

### 3. Database Service (Optional)
```
Service Type: PostgreSQL
Name: revisa-database
```

## 🚀 Deployment Commands

### If using root deployment (current setup):
```bash
# Commit your changes
git add .
git commit -m "Configure Railway deployment"
git push origin main

# Railway will auto-deploy
```

### If using separate services:
```bash
# Same - Railway watches your repo
# Each service deploys when its directory changes
```

## 🔍 Troubleshooting

### Build Fails with "No start command"
- ✅ Check `railway.json` has correct `startCommand`
- ✅ Verify the path in `startCommand` exists after build
- ✅ Set environment variables in Railway dashboard

### Build Takes Too Long
- ✅ Switch to Dockerfile deployment (faster)
- ✅ Use Railway's build cache (automatic)

### App Crashes on Start
- ✅ Check logs in Railway dashboard
- ✅ Verify environment variables are set
- ✅ Ensure `PORT` environment variable is set

### Frontend Can't Connect to API
- ✅ Set `VITE_API_URL` to your server's Railway URL
- ✅ Enable CORS in your NestJS app for Railway domain

## 🎯 Recommended Approach

**For your first deployment:**
1. **Use the current root `railway.json`** (deploys server only)
2. **Set environment variables** in Railway dashboard
3. **Test the API** at your Railway URL
4. **Deploy frontend separately** once server works

**For production:**
1. **Deploy as separate services** (server + frontend)
2. **Add PostgreSQL database**
3. **Set up custom domains**
4. **Configure monitoring**

## 🔗 Useful Railway Commands

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login
railway login

# Deploy from CLI
railway up

# View logs
railway logs

# Set environment variables
railway variables set NODE_ENV=production
```

## 💡 Pro Tips

1. **Start simple** - Deploy server first, then frontend
2. **Use Railway's PostgreSQL** - It's free and easy
3. **Check logs** - Railway dashboard has excellent logging
4. **Set up monitoring** - Railway has built-in metrics
5. **Use custom domains** - Railway provides free subdomains

Your monorepo is Railway-ready! The configuration files are set up correctly.
