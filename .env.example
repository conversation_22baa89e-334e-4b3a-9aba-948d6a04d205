# Revisa AI - Environment Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (development, staging, production)
NODE_ENV=production

# Application Ports
SERVER_PORT=3001
WWW_PORT=3002

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# NestJS Server Settings
SERVER_HOST=0.0.0.0
SERVER_CORS_ORIGIN=http://localhost:3002

# API Configuration
API_PREFIX=api
API_VERSION=v1

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# React Router Settings
VITE_API_URL=http://localhost:3001
VITE_APP_NAME=Revisa AI
VITE_APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION (Future Use)
# =============================================================================

# PostgreSQL Database
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=revisa_ai
# DB_USERNAME=revisa_user
# DB_PASSWORD=your_secure_password

# =============================================================================
# REDIS CONFIGURATION (Future Use)
# =============================================================================

# Redis Cache
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password

# =============================================================================
# SECURITY SETTINGS (Future Use)
# =============================================================================

# JWT Configuration
# JWT_SECRET=your_super_secret_jwt_key_here
# JWT_EXPIRES_IN=7d

# =============================================================================
# EXTERNAL SERVICES (Future Use)
# =============================================================================

# Email Service
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# =============================================================================
# MONITORING & LOGGING (Future Use)
# =============================================================================

# Log Level (error, warn, info, debug)
LOG_LEVEL=info

# Sentry DSN for error tracking
# SENTRY_DSN=your_sentry_dsn_here
